<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2022-11-07 11:17:21
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-25 09:30:07
-->
<template>
  <div class="wrapper" id="wrapper">
    <div class="TopTitle">
      <div class="tabs">
        <div
          class="tab"
          :class="{ 'has-dropdown': item.name === '强补固拓' }"
          v-for="(item, i) in tab1"
          :key="i"
          @click="handleTabClick(item)"
          @mouseenter="item.name === '强补固拓' && showDropdown()"
          @mouseleave="item.name === '强补固拓' && hideDropdown()"
        >
          <!-- <div class="icon" :style="{backgroundImage:'url('+item.icon+')'}"></div> -->
          <div class="tabName" :class="item.name === titleName ? 'active_name' : ''">{{ item.name }}</div>
          <div v-if="item.name === '强补固拓' && showCylDropdown" class="cyl-dropdown" @click.stop>
            <div
              class="cyl-dropdown-item"
              :class="{ selected: selectedCylId === opt.id }"
              v-for="opt in cylOptions"
              :key="opt.id"
              @click.stop="selectCyl(opt.id)"
            >
              {{ opt.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="mainTitle">
        <div class="title">{{ titleName }}</div>
      </div>
      <div class="tabs" style="margin-left: 1500px">
        <div class="tab" v-for="(item, i) in tab2" :key="i" @click="handleTabClick(item)">
          <div class="tabName" :class="item.name === titleName ? 'active_name' : ''">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <router-view></router-view>
  </div>
</template>

<script>
import { getCsdnInterface } from '@/api/csdnIndexApi'

export default {
  components: {},
  data() {
    return {
      titleName: '工业智服',
      tab1: [
        { name: '产业智服', router: '/qyzf' },
        { name: '强补固拓', router: '/qyzfCyl' },
      ],
      tab2: [
        { name: '人口全域感知', router: '/rkhl' },
        {
          name: '风险预警分析',
          router: '/fxyjfx',
        },
      ],
      showCylDropdown: false,
      selectedCylId: null, // 当前选中的产业链ID
      cylOptions: [
        { id: 210, name: '食品加工产业链' },
        { id: 211, name: '金属制造产业链' },
        { id: 212, name: '光电子信息产业链' },
      ],
      time: '',
      date: '',
      timer: '',
      weather: {
        weatherIcon: '',
        temp: '',
        wind: '',
      },
      dialogShow: false,
      hideMapRoutes: ['/IndicatorCenter'],
    }
  },
  mounted() {
    this.updateSelectedCyl()
    this.updateTitleByRoute()
  },
  watch: {
    $route() {
      this.updateSelectedCyl()
      this.updateTitleByRoute()
    },
  },
  methods: {
    titleClick() {
      // this.$router.push('/qyzf')
    },
    pageJump(r) {
      this.$router.push(r)
    },
    handleTabClick(item) {
      this.titleName = item.name
      if (item.name === '强补固拓') {
        // 如果当前不在 /dyCyl 页面，则跳转到新能源汽车
        this.$router.push({ path: '/qyzfCyl' })
      } else {
        this.pageJump(item.router)
      }
    },
    showDropdown() {
      this.showCylDropdown = true
    },
    hideDropdown() {
      this.showCylDropdown = false
    },
    selectCyl(id) {
      this.showCylDropdown = false
      this.titleName = '强补固拓'
      this.$router.push({ path: '/dyCyl', query: { id: String(id) } })
    },
    updateSelectedCyl() {
      // 如果当前在 dyCyl 页面，根据路由参数设置选中状态
      if (this.$route.path === '/dyCyl' && this.$route.query.id) {
        this.selectedCylId = parseInt(this.$route.query.id)
      } else {
        this.selectedCylId = null
      }
    },
    updateTitleByRoute() {
      const path = this.$route.path
      if (path === '/qyzfCyl' || path === '/dyCyl') {
        this.titleName = '强补固拓'
      } else if (path.startsWith('/qyzf')) {
        this.titleName = '工业智服'
      } else if (path === '/rkhl') {
        this.titleName = '人口全域感知'
      } else if (path === '/fxyjfx') {
        this.titleName = '风险预警分析'
      }
    },
  },
}
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background-size: cover;
  .TopTitle {
    width: 7680px;
    height: 240px;
    background: url('../../assets/common/topbackground.png') no-repeat;
    background-size: cover;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    .tabs {
      width: 2993px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .tab {
        cursor: pointer;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-left: 440px;
        .icon {
          width: 58px;
          height: 55px;
          background-size: cover;
        }
        .tabName {
          white-space: nowrap;
          font-weight: 700;
          font-size: 64px;
          color: #fff;
          margin-left: 17px;
        }
        .active_name {
          background: linear-gradient(90deg, #ffffff 0%, #b9ccff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .has-dropdown {
        position: relative;
        padding-bottom: 10px;
      }
      .cyl-dropdown {
        position: absolute;
        top: 90px;
        left: 0;
        background: rgba(0, 0, 0, 0.7);
        border: 1px solid #5594c9;
        padding: 8px 0;
        min-width: 420px;
        z-index: 1000;
      }
      .cyl-dropdown-item {
        padding: 12px 20px;
        font-size: 36px;
        color: #fff;
        white-space: nowrap;
        cursor: pointer;
      }
      .cyl-dropdown-item + .cyl-dropdown-item {
        border-top: 1px solid rgba(255, 255, 255, 0.12);
      }
      .cyl-dropdown-item:hover {
        color: #c2e5ff;
      }
      .cyl-dropdown-item.selected {
        color: #5594c9;
        font-weight: bold;
      }
      .cyl-dropdown-item.selected:hover {
        color: #c2e5ff;
      }
    }
    .mainTitle {
      cursor: pointer;
      width: 1694px;
      height: 154px;
      background-size: cover;
      text-align: center;
      position: absolute;
      left: 2993px;
      .title {
        margin-top: 40px;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 130px;
        color: #ffffff;
        line-height: 74px;
      }
    }
  }
}
</style>
