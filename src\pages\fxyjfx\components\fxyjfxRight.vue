<!--
 * @Description: 产业链右侧组件
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-18 14:08:20
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-19 09:14:38
-->
<template>
  <div class="fxyjfx_right">
    <MainTitle title="城市活力分析报告（月报）" size="large" />
    <div class="right1">
      <div class="filter_box">
        <el-select v-model="month" placeholder="月份">
          <el-option v-for="item in monthList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="country" placeholder="区域">
          <el-option v-for="item in countryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-input placeholder="请输入内容" v-model="monthInput" class="search_input" style="width: 648px">
          <el-button slot="append" class="search_btn">搜索</el-button>
        </el-input>
      </div>
      <CommonTable :height="'410px'" :tableData="tableData" style="margin-top: 16px" :showIndex="true"></CommonTable>
    </div>
    <MainTitle title="城市活力分析报告（周报）" size="large" />
    <div class="right2">
      <div class="filter_box">
        <el-select v-model="week" placeholder="月份">
          <el-option v-for="item in weekList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="weekCountry" placeholder="区域">
          <el-option v-for="item in countryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-input placeholder="请输入内容" v-model="weekInput" class="search_input" style="width: 648px">
          <el-button slot="append" class="search_btn">搜索</el-button>
        </el-input>
      </div>
      <CommonTable :height="'410px'" :tableData="tableData1" style="margin-top: 16px" :showIndex="true"></CommonTable>
    </div>
    <MainTitle title="活力专题分析报告" size="large" />
    <div class="right3">
      <CommonTable :height="'410px'" :tableData="tableData2" style="margin-top: 16px" :showIndex="true"></CommonTable>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import MainTitle from '@/components/MainTitle.vue'
import CommonTable from './commonTable.vue'
export default {
  name: 'fxyjfxRight',
  components: { MainTitle, CommonTable },
  data() {
    return {
      //城市活力分析报告月报
      monthList: [
        { label: '1月', value: '1' },
        { label: '2月', value: '2' },
        { label: '3月', value: '3' },
        { label: '4月', value: '4' },
        { label: '5月', value: '5' },
        { label: '6月', value: '6' },
        { label: '7月', value: '7' },
        { label: '8月', value: '8' },
        { label: '9月', value: '9' },
        { label: '10月', value: '10' },
        { label: '11月', value: '11' },
        { label: '12月', value: '12' },
      ],
      month: '',
      countryList: [
        {
          label: '王义贞镇',
          value: 1,
        },
        {
          label: '孛畈镇',
          value: 2,
        },
        {
          label: '烟店镇',
          value: 3,
        },
        {
          label: '雷公镇',
          value: 4,
        },
        {
          label: '伏水镇',
          value: 5,
        },
        {
          label: '接官乡',
          value: 6,
        },
        {
          label: '赵棚镇',
          value: 7,
        },

        {
          label: '陈店乡',
          value: 8,
        },
        {
          label: '木梓乡',
          value: 9,
        },
        {
          label: '棠棣镇',
          value: 10,
        },
        {
          label: '李店镇',
          value: 11,
        },

        {
          label: '开发区',
          value: 12,
        },
        {
          label: '府城街道',
          value: 13,
        },
        {
          label: '南城街道',
          value: 14,
        },
        {
          label: '巡店镇',
          value: 15,
        },
        {
          label: '辛榨乡',
          value: 16,
        },
      ],
      country: '',
      monthInput: '',
      tableData: {
        thead: [
          { label: '月报名称', property: 'name', width: 1700, align: 'center' },
          { label: '操作', property: 'handle', width: 360, align: 'center' },
        ],
        tbody: [
          {
            name: '2025年05月城市活力分析报告（安陆市）',
          },
          {
            name: '2025年06月城市活力分析报告（安陆市）',
          },
          {
            name: '2025年07月城市活力分析报告（安陆市）',
          },
          {
            name: '2025年08月城市活力分析报告（安陆市）',
          },
        ],
      },
      //城市活力分析报告周报
      weekList: [
        { label: '第1周', value: '1' },
        { label: '第2周', value: '2' },
        { label: '第3周', value: '3' },
        { label: '第4周', value: '4' },
        { label: '第5周', value: '5' },
        { label: '第6周', value: '6' },
        { label: '第7周', value: '7' },
        { label: '第8周', value: '8' },
        { label: '第9周', value: '9' },
        { label: '第10周', value: '10' },
        { label: '第11周', value: '11' },
        { label: '第12周', value: '12' },
        { label: '第13周', value: '13' },
        { label: '第14周', value: '14' },
      ],
      week: '',
      weekCountry: '',
      weekInput: '',
      tableData1: {
        thead: [
          { label: '月报名称', property: 'name', width: 1700, align: 'center' },
          { label: '操作', property: 'handle', width: 360, align: 'center' },
        ],
        tbody: [
          {
            name: '2025年安陆市人口分析报告第10周',
          },
          {
            name: '2025年安陆市人口分析报告第11周',
          },
          {
            name: '2025年安陆市人口分析报告第12周',
          },
          {
            name: '2025年安陆市人口分析报告第13周',
          },
        ],
      },
      //活力专题分析报告
      tableData2: {
        thead: [
          { label: '月报名称', property: 'name', width: 1700, align: 'center' },
          { label: '操作', property: 'handle', width: 360, align: 'center' },
        ],
        tbody: [
          {
            name: '安陆市2025五一假期人口报告',
          },
          {
            name: '安陆市2025春节分析报告',
          },
          {
            name: '安陆市2024国庆假期人口报告',
          },
          {
            name: '安陆市2024春节人口报告',
          },
        ],
      },
    }
  },

  created() {
    this.getData()
  },

  mounted() {},

  beforeDestroy() {},

  methods: {},
}
</script>

<style scoped lang="less">
.fxyjfx_right {
  width: 2262px;
  .right1 {
    height: 550px;
  }
  .right2 {
    height: 550px;
  }
  .right3 {
    height: 460px;
  }
  .filter_box {
    display: flex;
    align-content: center;
    align-items: center;
    margin-top: 24px;
    /deep/.el-select {
      width: 210px;
      height: 75px;
      margin-left: 40px;
      background: rgba(0, 52, 117, 0.8);
      border-radius: 10px 10px 10px 10px;
      border: 3px solid #1a5cb5;
      box-sizing: border-box;
      .el-input__inner {
        font-size: 36px !important;
        border: none !important;
        background: transparent;
        height: 75px;
        line-height: 75px !important;
      }
      .el-select__caret {
        font-size: 36px !important;
      }
      .el-input__icon {
        line-height: 75px !important;
        width: 40px !important;
      }
    }
    /deep/.search_input {
      height: 75px;
      margin-left: 40px;
      background: rgba(0, 52, 117, 0.8);
      border-radius: 10px 10px 10px 10px;
      border: 3px solid #1a5cb5;
      .el-input__inner {
        font-size: 36px !important;
        border: none !important;
        background: transparent;
        height: 75px;
        line-height: 75px !important;
      }
      .el-input-group__append {
        border: none !important;
        background: transparent;
      }
      .search_btn {
        width: 125px;
        height: 75px;
        background: linear-gradient(360deg, #003ca6 0%, #387af0 100%);
        border: none;
        font-size: 33px;
        color: #fefefe;
        line-height: 48px;
      }
    }
  }
}
</style>
<style lang="less">
.el-select-dropdown__item {
  font-size: 28px !important;
}
</style>