<template>
  <div class="commonTable" :class="[showTwoLine ? 'twoLineTable' : '', cursor ? 'showCursor' : '']">
    <el-table
      :height="height"
      stripe
      ref="table"
      :show-summary="summaryFlag"
      :cell-style="cellStyle"
      :cell-class-name="cellClass"
      :header-cell-style="headerStyle"
      :row-class-name="tableRowClassName"
      :data="tableData.tbody"
      @row-click="rowClick"
      @cell-mouse-enter="handleMouseenter(true)"
      @header-click="headerItemClick"
      @cell-mouse-leave="handleMouseleave(false)"
      :highlight-current-row="highlightCurrentRow"
    >
      <el-table-column v-if="showIndex" label="序号" type="index" width="144" align="center"></el-table-column>
      <template v-for="(item, index) in tableData.thead">
        <el-table-column
          v-if="item.property === 'warning_level'"
          align="center"
          :show-overflow-tooltip="tooltip"
          :key="index"
          :min-width="item.width"
          :label="item.label"
          :property="item.property"
        >
          <template slot-scope="scope">
            <span class="red_color" v-if="scope.row.warning_level == '红色'">{{ scope.row.warning_level }}</span>
            <span class="org_color" v-if="scope.row.warning_level == '橙色'">{{ scope.row.warning_level }}</span>
            <span class="yellow_color" v-if="scope.row.warning_level == '黄色'">{{ scope.row.warning_level }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :align="item.align"
          :show-overflow-tooltip="tooltip"
          :key="index"
          :min-width="item.width"
          :label="item.label"
          :property="item.property"
          :formatter="item.formatter"
        ></el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'comTable',
  props: {
    showTwoLine: {
      type: Boolean,
      default: false,
    },
    cursor: {
      type: Boolean,
      default: false,
    },
    showIndex: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    tooltip: {
      type: Boolean,
      default: true,
    },
    summaryFlag: {
      type: Boolean,
      default: false,
    },
    height: {
      type: [String, Number],
      default: '300px',
    },
    tableData: {
      type: Object,
      default: () => {
        return {}
      },
    },
    rowClick: {
      type: Function,
      default: () => {},
    },
    rowClickIndex: {
      type: Number,
      default: -1,
    },
    headerStyle: {
      type: Function,
      default: () => {
        return 'background-color:rgba(11, 100, 195, 0.60);font-size:32px;line-height:40px; color:#fff;border-color:rgba(11, 100, 195, 0.60)'
      },
    },
    highlightCurrentRow: {
      type: Boolean,
      default: false,
    },
    isRoll: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rolltimer: null,
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.mouseleave()
    })
  },
  beforeDestroy() {
    this.mouseenter()
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      row.row_index = rowIndex
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if ((rowIndex + 1) % 2 === 0) {
        return 'background-color:rgba(0, 74, 166, 0.3); font-size: 32px;color:#FFFFFF;border-color:transparent'
      } else {
        return 'background-color:transparent; font-size: 32px;color:#FFFFFF;border-color:transparent'
      }
    },
    cellClass({ row, column, rowIndex, columnIndex }) {
      if (this.rowClickIndex == rowIndex) {
        return 'yellow_linear_table'
      }
    },
    autoRoll(stop) {
      if (stop) {
        clearInterval(this.rolltimer)
        this.rolltimer = null
        return
      }
      const table = this.$refs.table
      const bodyWrapper = table.bodyWrapper
      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果
      this.rolltimer = setInterval(() => {
        // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
        if (bodyWrapper.clientHeight + bodyWrapper.scrollTop >= bodyWrapper.scrollHeight) {
          bodyWrapper.scrollTop = 0
        } else {
          bodyWrapper.scrollTop++
        }
      }, 5 * 10)
    },
    handleMouseleave() {
      this.isRoll && this.autoRoll(false)
    },
    handleMouseenter() {
      this.isRoll && this.autoRoll(true)
    },
    mouseenter() {
      this.isRoll && this.autoRoll(true)
    },
    mouseleave() {
      this.isRoll && this.autoRoll()
    },
    headerItemClick(e) {
      console.log(e, e.label.data.class, 'headerItemClick')
    },
  },
}
</script>

<style lang="less" scoped>
// 去除滚动的滚动条
::v-deep .el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: auto;
}
/deep/.fontBold {
  .cell {
    color: red;
  }
}
/deep/.el-table tr {
  background-color: unset;
  /* background-color: #FFF; */
}
/deep/.el-table .el-table__cell {
  padding: 0;
}
/deep/ .el-table .cell {
  line-height: 92px;
  padding-left: 32px;
}
.twoLineTable /deep/.el-table th.el-table__cell > .cell {
  height: 160px;
  line-height: 46px;
  display: flex;
  align-content: center;
  align-items: center;
}
/deep/.table__cell.is-leaf {
  border-bottom: 1px solid transparent;
}
/deep/ .el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: transparent;
}
/deep/ .el-table,
.el-table__expanded-cell {
  background-color: transparent;
}
/* 表格内背景颜色 */
.el-table th,
.el-table tr,
.el-table td {
  background-color: unset;
}
/deep/ .el-table {
  .current-row {
    background: rgba(0, 182, 243, 0.3);
    > td {
      border-top: 1px solid #00b6f3 !important;
      border-bottom: 1px solid #00b6f3 !important;
    }
  }
}
/deep/.el-table th.el-table__cell > .cell {
  padding-left: 32px;
}
//合计
/deep/.el-table__footer-wrapper {
  .el-table__cell {
    height: 92px;
    background-color: transparent;
    background-image: linear-gradient(0deg, rgba(51, 149, 205, 0.2) 1%, rgba(63, 134, 255, 0.3) 100%);
    font-size: 32px;
    color: #ffffff;
    border-color: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  }
}
/deep/.yellow_linear_table {
  .cell {
    background: linear-gradient(0deg, #fff120 0%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.red_color {
  color: #ff1717;
}
.org_color {
  color: #ff7817;
}
.yellow_color {
  color: #faad14;
}
.showCursor {
  /deep/.el-table__row {
    cursor: pointer;
  }
}
</style>
<style lang="less">
.el-tooltip__popper {
  font-size: 32px !important;
}
</style>
