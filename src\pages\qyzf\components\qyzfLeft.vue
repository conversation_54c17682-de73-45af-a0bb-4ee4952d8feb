<template>
  <div class="qyhxleft-container">
    <!-- 市场主体 -->
    <div>
      <MainTitle title="市场概况" size='large'/>
      <div class="con">
        <div class="con-item">
          <!-- 内容容器 -->
          <div class="item-content" style="margin-top: 20px; height: 800px">
            <div class="qyyjqy_top">
              <div class="qyyjqy_top_2">
                <div
                  v-for="(item, index) in qyzs.toLocaleString()"
                  :key="index"
                  class="count-toNum s-c-yellow-gradient"
                >
                  <count-to
                    v-if="!isNaN(Number(item))"
                    :start-val="0"
                    :end-val="Number(item)"
                    :duration="3000"
                  ></count-to>
                  <i v-else>{{ item }}</i>
                </div>
                <div style="margin-top: 20px; margin-left: 10px; font-size: 36px">家</div>
              </div>
              <div class="qyyjqy_top_1">市场主体数</div>
            </div>
            <div class="qylx">
              <div class="svg">
                <img src="@/assets/qyzf/img1.png" alt="" />
                <img src="@/assets/qyzf/img2.png" alt="" />
                <img src="@/assets/qyzf/img3.png" alt="" />
                <img src="@/assets/qyzf/img4.png" alt="" />
                <img src="@/assets/qyzf/img5.png" alt="" />
              </div>
              <li v-for="(item, index) in qyflData" :key="index" class="cursor" :title="getTitle(index)">
                <div class="text-con" @click="openDialog(item)">
                  <div>{{ item.name }}({{ item.unit }})</div>
                  <div class="text-bg">
                    {{ item.value }}
                  </div>
                </div>
              </li>
            </div>
          </div>
        </div>
        <div class="con-item" style="position: relative">
          <div class="scgk-right">
            <div class="scgk-right-item" v-for="(item, index) in lists">
              <p class="item_name">{{ item.name }}</p>
              <p>
                <span class="item_value s-c-yellow-gradient s-w7">{{ item.value }}</span>
                <span class="item_unit">{{ item.unit }}</span>
                <!-- <span v-show="index == 4 || index == 5" class="s-c-red-gradient s-font-60">
                  <i class="el-icon-top"></i>
                </span> -->
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <MainTitle title="产业概况" size='large'/>
    <div class="con flex-c-c">
      <div>
        <SubTitle title="规上工业总产值" />
        <div style="position: relative">
          <div id="chart03" style="width: 100%; height: 740px"></div>
        </div>
      </div>
      <div>
        <SubTitle title="进出口总额" />
        <div style="position: relative">
          <div id="chart04" style="width: 100%; height: 740px"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import MainTitle from '@/components/MainTitle.vue'
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'qyzfLeft',
  components: { MainTitle, SubTitle },
  data() {
    return {
      //企业总数
      qyzs: '-',
      //企业分类统计
      qyflData: [
        {
          icon: '@/assets/qyzf/zxqy.png',
          name: '规上企业数',
          value: '-',
          unit: '家',
        },
        {
          icon: '@/assets/qyzf/gsqy.png',
          name: '上市企业数',
          value: '-',
          unit: '家',
        },

        {
          icon: '@/assets/qyzf/xwqy.png',
          name: '小微企业数',
          value: '-',
          unit: '家',
        },
        {
          icon: '@/assets/qyzf/gtgsh.png',
          name: '个体工商户数',
          value: '-',
          unit: '家',
        },
      ],
      lists: [
        {
          name: '地区生产总值',
          value: '6925.52',
          unit: '亿元',
        },
        {
          name: '一般公共预算收入',
          value: '536.80',
          unit: '亿元',
        },
        {
          name: '规上工业增加值',
          value: '1329.47',
          unit: '亿元',
        },
        {
          name: '规上工业出口交货值',
          value: '1621.81',
          unit: '亿元',
        },
        {
          name: '规上工业增加值增速',
          value: '9.1',
          unit: '%',
        },
        {
          name: '固定资产投资增速',
          value: '5.3',
          unit: '%',
        },
      ],
    }
  },
  mounted() {
    this.initApi()
  },
  methods: {
    openDialog(item) {
      if (item.name != '个体工商户数') {
        this.$emit('openDialog', item.name)
      }
    },
    format(val) {
      if (val.length > 4) {
        return val.slice(0, 4) + '...'
      } else {
        return val
      }
    },
    formatNumber(num) {
      return num.toString().replace(/\d+/, function (n) {
        return n.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
      })
    },
    initApi() {
      this.getData()
      this.getData1()
      this.queryGsqyData(12)
      this.queryGsqyDataTwo(12)
    },
    getData1() {
      getCsdnInterface1('qyhx_scgk_al').then((res) => {
        let resdata = res.data.data
        console.log(res)
        this.lists = resdata
      })
    },
    getData() {
      getCsdnInterface1('qyhx_scgk_qxld', { qxwd: '开发区' }).then((res) => {
        let qylx = [
          {
            tjz: 0,
            ywwd1: '市场主体',
          },
          {
            tjz: 0,
            ywwd1: '个体工商户',
          },
          {
            tjz: 0,
            ywwd1: '小微企业',
          },
          {
            tjz: 0,
            ywwd1: '规上企业',
          },
          {
            tjz: 0,
            ywwd1: '科技型企业',
          },
          {
            tjz: 0,
            ywwd1: '专精特新中小企业',
          },
          {
            tjz: 0,
            ywwd1: '创新型中小企业',
          },
          {
            tjz: 0,
            ywwd1: '专精特新小巨人企业',
          },
          {
            tjz: 0,
            ywwd1: '上市企业',
          },
          {
            tjz: 0,
            ywwd1: '隐形冠军企业',
          },
          {
            tjz: 0,
            ywwd1: '众创空间',
          },
          {
            tjz: 0,
            ywwd1: '科技小巨人企业',
          },
          {
            tjz: 0,
            ywwd1: '科技企业孵化器',
          },
          {
            tjz: 0,
            ywwd1: '技术创新示范企业',
          },
          {
            tjz: 0,
            ywwd1: '工程技术研究中心',
          },
          {
            tjz: 0,
            ywwd1: '技术先进型服务企业',
          },
          {
            tjz: 0,
            ywwd1: '独角兽企业',
          },
        ]
        let resdata = res.data.data
        qylx.forEach((ele, i) => {
          ele.tjz =
            resdata.filter((a) => a.ywwd1 == ele.ywwd1).length > 0
              ? resdata.filter((a) => a.ywwd1 == ele.ywwd1)[0].tjz
              : 0
        })
        this.qyflData[0].value = Number(qylx[3].tjz).toLocaleString()
        this.qyflData[1].value = Number(qylx[8].tjz).toLocaleString()
        this.qyflData[2].value = Number(qylx[2].tjz).toLocaleString()
        this.qyflData[3].value = Number(qylx[1].tjz).toLocaleString()
        this.qyzs = this.formatNumber(qylx[0].tjz)
      })
    },
    queryGsqyData(yf) {
      getCsdnInterface1('qyhx_sy_gsqyyysr', { yf: yf }).then((res) => {
        let data = res.data.data.map((item) => {
          return {
            name: item.sscyl,
            value: item.gsqyyysr,
          }
        })
        this.getChartBar1('chart03', data)
      })
    },

    queryGsqyDataTwo(yf) {
      getCsdnInterface1('qyhx_cyl_czfb', { yf: yf }).then((res) => {
        let mcArr = res.data.data.map((item) => {
          return item.cylmc
        })
        let valArr = res.data.data.map((item) => {
          return item.gsgyzcz
        })
        this.getChartBar2('chart04', mcArr, valArr)
      })
    },
    getChartBar1(id, data) {
      echarts.init(document.getElementById(id)).dispose()
      let myChart = echarts.init(document.getElementById(id))
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: 'rgba(9, 24, 48, 0.5)',
          borderColor: 'rgba(75, 253, 238, 0.4)',
          textStyle: {
            color: '#CFE3FC',
            fontSize: '36',
          },
          borderWidth: 1,
        },
        grid: {
          top: '20%',
          right: '5%',
          left: '15%',
          bottom: '25%',
        },
        xAxis: [
          {
            type: 'category',
            data: data.map((item) => {
              return item.name
            }),
            axisLine: {
              lineStyle: {
                color: 'rgba(122, 163, 197, 1)',
              },
            },
            axisLabel: {
              // margin: 10,
              // interval: 0,
              margin: 20,
              rotate: -30,
              color: '#fff',
              textStyle: {
                fontSize: 24,
              },
              formatter: function (name) {
                if (name.length > 4) {
                  return name.slice(0, 4) + '...'
                } else {
                  return name
                }
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            name: '规上企业营业收入（亿元）',
            nameTextStyle: {
              color: '#fff',
              fontSize: 28,
              padding: [0, 0, 20, 260],
            },
            axisLabel: {
              formatter: '{value}',
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(122, 163, 197, 0)',
              },
            },
          },
        ],
        graphic: [
          {
            type: 'text',
            left: '50%',
            top: '50%',
            silent: true,
            invisible: data.length,
            style: {
              width: 200,
              fill: 'white',
              text: '暂无数据',
              textAlign: 'center',
              font: '32px system-ui',
            },
          },
        ],
        series: [
          {
            type: 'bar',
            data: data.map((item) => {
              return item.value
            }),
            barWidth: '20px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(19, 194, 194, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(19, 194, 194,0)', // 100% 处的颜色
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0,160,221,1)',
                shadowBlur: 4,
              },
            },
            label: {
              normal: {
                show: false,
              },
            },
          },
        ],
      }
      myChart.setOption(option, true)
    },
    getChartBar2(id, mcArr, valArr) {
      let myChart = echarts.init(document.getElementById(id))
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: 'rgba(9, 24, 48, 0.5)',
          borderColor: 'rgba(75, 253, 238, 0.4)',
          textStyle: {
            color: '#CFE3FC',
            fontSize: '36',
          },
          borderWidth: 1,
        },
        grid: {
          top: '20%',
          right: '5%',
          left: '10%',
          bottom: '25%',
        },
        xAxis: [
          {
            type: 'category',
            data: mcArr,
            axisLine: {
              lineStyle: {
                color: 'rgba(122, 163, 197, 1)',
              },
            },
            axisLabel: {
              margin: 20,
              rotate: -30,
              color: '#fff',
              textStyle: {
                fontSize: 24,
              },
              formatter: function (name) {
                if (name.length > 4) {
                  return name.slice(0, 4) + '...'
                } else {
                  return name
                }
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            name: '产值分布（亿元）',
            nameTextStyle: {
              color: '#fff',
              fontSize: 28,
              padding: [0, 0, 20, 100],
            },
            axisLabel: {
              formatter: '{value}',
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(122, 163, 197, 0)',
              },
            },
          },
        ],
        series: [
          {
            type: 'bar',
            data: valArr,
            // data: [582, 436, 114, 230, 308, 453, 379, 93, 229, 401],
            barWidth: '20px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 144, 255, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 144, 255,0)', // 100% 处的颜色
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0,160,221,1)',
                shadowBlur: 4,
              },
            },
            label: {
              normal: {
                show: false,
              },
            },
          },
        ],
      }
      myChart.setOption(option, true)
    },
    getTitle(index) {
      if (index == 0) {
        return '规上企业包括规模以上工业、有资质的建筑业、限额以上批发和零售业、限额以上住宿和餐饮业、有开发经营活动的全部房地产开发经营业、规模以上服务业法人单位'
      } else {
        return false
      }
    },
  },
}
</script>

<style scoped lang="less">
* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

p {
  margin: 0;
  margin: 0;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.cursor {
  cursor: pointer;
}

.no-cursor {
  cursor: no-drop;
}

.qyhxleft-container {
  width: 2070px;
  height: 1904px;
  padding: 10px 55px 30px;
  box-sizing: border-box;
  /* background-color: #133055; */
}

.conNew {
  display: flex;
  flex-direction: column;
}

.con {
  width: 100%;
  height: 815px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-top: 32px;
}

.con > div {
  width: 49%;
  overflow: hidden;
}

.item-content {
  width: 100%;
  height: 770px;
  position: relative;
  overflow-y: auto;
}

/* .title {
  width: 100%;
  text-align: center;
  font-size: 44px;
  font-style: italic;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: absolute;
} */

.qyyjqy_top {
  width: 100%;
  text-align: center;
  font-size: 32px;
  color: #dcefff;
  position: absolute;
  top: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.qyyjqy_top_1 {
  margin-top: 20px;
}

.qyyjqy_top_2 {
  display: flex;
}

.qylx {
  position: relative;
  width: 100%;
  height: 95%;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
  flex-wrap: wrap;
  /* background-image: url('/static/citybrain/qyhx/images/index/sczt-center.png'); */
  background-repeat: no-repeat;
  background-position: center;
}

.qylx .svg,
.qylx .svg img {
  position: absolute;
}

.qylx .svg img:first-child {
  top: 164px;
  left: 107px;
  animation: img1 10s infinite alternate linear;
}

.qylx .svg img:nth-child(2) {
  top: 157px;
  left: 158px;
}

.qylx .svg img:nth-child(3) {
  top: 154px;
  left: 183px;
  animation: img3 20s infinite alternate linear;
}

.qylx .svg img:nth-child(4) {
  top: 156px;
  left: 277px;
  animation: img4 30s infinite alternate linear;
}

.qylx .svg img:nth-child(5) {
  top: 50px;
  left: 175px;
  animation: img3 20s infinite alternate linear;
}

@keyframes img1 {
  0% {
    transform: rotate3d(1, 1, 1, 0deg);
  }

  50% {
    transform: rotate3d(1, 1, 1, 45deg);
  }

  100% {
    transform: rotate3d(1, 1, 1, 0deg);
  }
}

@keyframes img3 {
  0% {
    transform: rotateX(0deg);
  }

  50% {
    transform: rotateX(360deg);
  }

  100% {
    transform: rotateX(720deg);
  }
}

@keyframes img4 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(900deg);
  }
}

.qylx li {
  /* width: 50%; */
  display: flex;
  align-items: center;
  position: absolute;
}

.cursor:nth-of-type(1) {
  left: 0;
  top: 0;
}

.cursor:nth-of-type(2) {
  right: 0;
  top: 0;
}

.cursor:nth-of-type(3) {
  bottom: -150px;
  left: 0;
}

.cursor:nth-of-type(4) {
  right: 0;
  bottom: -150px;
}

.text-con {
  min-width: 230px;
  margin-left: 20px;
  font-size: 40px;
  color: #dcefff;
  /* width:403px; */
  height: 346px;
  background: url('../img/scgk1.png') no-repeat 0px -25px;
  background-size: 100% 100%;
}

.cursor:nth-of-type(2) .text-con {
  background: url('../img/scgk2.png') no-repeat 0px -25px;
  background-size: 100% 100%;
}

.cursor:nth-of-type(3) .text-con {
  background: url('../img/scgk2.png') no-repeat 0px -25px;
  background-size: 100% 100%;
}

.text-bg {
  width: 100%;
  text-align: center;
  font-size: 58px;
  font-weight: 700;
  background: linear-gradient(90.01871471181363deg, #ffffff 0%, #b9ccff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text {
  font-style: normal;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unit {
  font-size: 36px;
}

.text1 {
  font-size: 25px;
  margin-top: 18px;
  margin-left: 10px;
}

.sczt-left,
.sczt-echarts,
.cyfb-con,
.cyjj-con {
  width: 49%;
  height: 100%;
}

#cyfb {
  width: 100%;
  height: 500px;
}

#cyl-echarts,
#zdqy-echarts {
  width: 100%;
  height: 430px;
}
.count-toNum {
  /* width: 50px; */
  height: 74px;
  line-height: 74px;
  text-align: center;
  font-size: 74px;
  font-weight: 700;
}

#cyjj-chart {
  width: 620px;
  height: 520px;
}

/* 表格 */
.table {
  width: 960px;
  height: 500px;
  /* padding: 10px; */
  /* box-sizing: border-box; */
}

.table1 {
  width: 980px;
  height: 740px;
}

.table1 .th {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  line-height: 60px;
  color: #dcefff;
  margin-left: 0;
  text-align: center;
}

.table1 .th_td {
  letter-spacing: 0px;
  text-align: center;
}

.table1 .tbody {
  width: 100%;
  height: calc(100% - 110px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table1 .tbody:hover {
  overflow-y: auto;
}

.table1 .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table1 .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table1 .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 78px;
  line-height: 78px;
  font-size: 28px;
  color: #dcefff;
  /* cursor: pointer; */
  box-sizing: border-box;
  background-color: #0f2b4d;
  /* border-bottom: 1px solid #1d3c5f; */
  margin-bottom: 0;
}

.table1 .tr_td {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  letter-spacing: 0px;
  text-align: left;
  box-sizing: border-box;
}

.table1 .tr_td > img {
  position: relative;
  top: 25px;
}

.table1 .tr:nth-child(2n) {
  background: #19345833;
  /* background: rgba(10, 39, 76); */
}

.table1 .tr:nth-child(2n + 1) {
  /* background: rgba(15, 49, 95); */
  background: #19345899;
}

.table1 .tr:hover {
  background-color: #3e74aa70;
}

/* 标题 */

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.title-item {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
}

.title-item > li {
  margin-right: 50px;
}

.title-item > li:last-child {
  margin-right: 0;
}

.title-item-active {
  color: #fff;
}

/* 惠企政策 */

.qhzc-bottom {
  width: 100%;
  height: 100px !important;
  font-size: 30px;
  position: absolute;
  bottom: 10px;
}

.hqzc-top {
  width: 100%;
  height: 590px;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
}

.zcdf {
  width: 100%;
  font-size: 30px;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.zcdfValue {
  font-size: 40px;
  color: #ff941d;
}

.data-page {
  width: 960px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 新的分页样式 */
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background: #5f7b96;
  color: #fff;
  font-size: 34px;
  font-weight: normal;
  height: 60px;
  line-height: 58px;
  box-sizing: border-box;
}

.el-pager li.active + li {
  border-left: 1px solid transparent !important;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background: #0166a6;
  border-radius: 3px 3px 3px 3px;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
}

.el-pager li {
  background: #5f7b96;
  padding: 0 10px;
  border: 1px solid transparent;
  box-sizing: border-box;
}

.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 30px;
}

.el-pagination .btn-next,
.el-pagination .btn-prev {
  width: 60px;
}

/* 分页- */
.el-pagination__total {
  color: #fff;
  font-size: 32px !important;
  margin: 0;
  padding-right: 20px;
}

/* 分页-多少页 */
.el-pagination__sizes .el-input .el-input__inner {
  font-size: 32px;
  background-color: transparent;
  border-color: #6f788a;
  height: 60px;
  line-height: 60px;
  color: #fff;
}

.el-pagination .el-select .el-input .el-input__inner {
  padding-right: 51px;
}

.el-select .el-input .el-select__caret {
  font-size: 40px;
  color: #6f788a;
  transform: rotate(0);
  padding-top: 10px;
}

.el-select .el-input .el-select__caret.is-reverse {
  transform: rotate(180deg);
  margin-top: -10px;
}

.el-pagination .el-select .el-input {
  width: 250px;
  margin: 0;
}

.el-pagination button,
.el-pagination span:not([class*='suffix']) {
  height: 60px;
  line-height: 60px;
}

.el-pagination .el-input__suffix {
  right: 15px;
}

.el-select-dropdown__item {
  font-size: 32px;
  background-color: transparent;
  color: #fff;
  border-color: #6f788a;
  margin-bottom: 10px;
}

.el-select-dropdown {
  background-color: #041330;
  border-color: #e4e7ed4f;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: transparent;
}

.el-popper[x-placement^='bottom'] .popper__arrow {
  display: none;
}

.el-pagination button,
.el-pagination span:not([class*='suffix']) {
  font-size: 32px;
  color: #fff;
}

.el-pagination__editor.el-input .el-input__inner {
  height: 60px;
  font-size: 32px;
  background-color: transparent;
  color: #fff;
  border-color: #6f788a;
}

.el-pagination__editor.el-input {
  width: 100px;
  height: 60px;
  margin: 0 10px;
}

/* 表格 */
.table {
  width: 100%;
  height: 800px;
  padding: 10px;
  box-sizing: border-box;
  overflow-y: auto;
}

.table1 .th {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  line-height: 60px;
  margin-left: 0;
  color: #dcefff;
}

.table1 .th_td {
  letter-spacing: 0px;
  text-align: left;
  padding-left: 20px;
  box-sizing: border-box;
  text-align: left;
}

.table1 .tbody {
  width: 100%;
  height: calc(100% - 60px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table1 .tbody:hover {
  overflow-y: auto;
}

.table1 .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table1 .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table1 .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 82px;
  line-height: 82px;
  font-size: 40px;
  /* border-bottom: 1px solid #1d3c5f; */
  color: #dcefff;
  /* cursor: pointer; */
  box-sizing: border-box;
  margin-bottom: 0 !important;
}

.table1 .tr:nth-child(2n) {
  background: #19345833;
}

.table1 .tr:nth-child(2n + 1) {
  /* background: #19345899; */
  background: linear-gradient(
    268deg,
    rgba(0, 121, 227, 0) 0%,
    rgba(0, 121, 227, 0.3384) 26%,
    rgba(70, 155, 227, 0.5882) 75%,
    rgba(8, 17, 26, 0) 100%
  );
}

.table1 .tr:hover {
  background-color: #3e74aa70;
}

.table1 .tr_td {
  letter-spacing: 0px;
  text-align: left;
  padding-left: 20px;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.table1 .tr_td > img {
  position: relative;
  top: 25px;
}

/********************************************** 人才服务右侧 **********************************************/
.ycWrap {
  display: flex;
  flex-direction: column;
  height: 380px;
  /* background-color: #acddff; */
  box-sizing: border-box;
  padding: 50px 0 50px 0;
}

.topStyle {
  display: flex;
  justify-content: center;
  align-items: baseline;
}

.midInClass {
  flex: 1;
  /* background-color: #ff941d; */
  display: flex;
  justify-content: center;
  align-items: center;
}

.zhuIdChart {
  width: 100%;
  height: 100%;
  /* background-color: #ff941d; */
}

.leftStyle {
  display: flex;
  align-items: baseline;
}

.normalTxt {
  font-size: 36px;
  color: #dcefff;
}

.mrr {
  margin-right: 190px;
}

.mrl {
  margin: 0 8px 0 20px;
}

.txtOneColor {
  font-weight: bolder;
  font-size: 72px;
  line-height: 72px;
  background: linear-gradient(180deg, #ff4f1d 0%, #ffa41d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.txtTwoColor {
  font-weight: bolder;
  font-size: 72px;
  line-height: 72px;
  /* color: #A0E3FF; */
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.txtThreeColor {
  font-weight: bolder;
  font-size: 72px;
  line-height: 72px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.boderOne {
  border-right: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 10%,
      rgba(101, 184, 255, 1) 50%,
      rgba(255, 255, 255, 0) 90%
    )
    2 2 2 2;
}

.boderBottom {
  border-bottom: 2px solid;
  border-image: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 10%,
      rgba(101, 184, 255, 1) 50%,
      rgba(255, 255, 255, 0) 90%
    )
    2 2 2 2;
}

.mrNew {
  width: 50%;
}

.mbNew {
  margin-bottom: 50px !important;
}

.finalImg {
  width: 40px;
  height: 44px;
  margin-left: 16px;
}

.scgk-right {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.scgk-right-item {
  width: 460px;
  height: 220px;
  background: url('../img/scgk-bg.png') no-repeat;
  background-size: 100% 100%;
  margin: 20px 0;
  padding: 35px 0px;
  box-sizing: border-box;
  text-align: center;
}

.item_name {
  font-size: 36px;
  color: #ffffff;
  /* white-space: nowrap; */
  height: 48px;
  line-height: 48px;
}

.item_value {
  font-size: 80px;
}

.item_unit {
  font-size: 36px;
  color: #cde7ff;
}

.cy_item {
  width: 33.3%;
  text-align: center;
  position: relative;
}

.cy_item_bottom {
  display: flex;
  justify-content: space-around;
}
* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

p {
  margin: 0;
  margin: 0;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.cursor {
  cursor: pointer;
}

.no-cursor {
  cursor: no-drop;
}

.qyhxleft-container {
  width: 2070px;
  height: 1904px;
  padding: 10px 55px 30px;
  box-sizing: border-box;
  /* background-color: #133055; */
}

.con {
  width: 100%;
  height: 815px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.con > div {
  width: 49%;
  overflow: hidden;
}

.item-content {
  width: 100%;
  height: 770px;
  position: relative;
  overflow-y: auto;
}

/* .title {
  width: 100%;
  text-align: center;
  font-size: 44px;
  font-style: italic;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: absolute;
} */

.qyyjqy_top {
  width: 100%;
  text-align: center;
  font-size: 32px;
  color: #dcefff;
  position: absolute;
  top: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.qyyjqy_top_1 {
  margin-top: 20px;
}

.qyyjqy_top_2 {
  display: flex;
}

.qylx {
  width: 100%;
  height: 95%;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
  flex-wrap: wrap;
  background-image: url('../img/sczt-center.png');
  background-repeat: no-repeat;
  background-position: center;
}

.qylx li {
  /* width: 50%; */
  display: flex;
  align-items: center;
}

.text-con {
  min-width: 230px;
  font-size: 32px;
  margin-left: 20px;
  color: #dcefff;
}

.text-bg {
  font-size: 56px;
  font-weight: 400;
}

.text {
  font-style: normal;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unit {
  font-size: 36px;
}

.text1 {
  font-size: 25px;
  margin-top: 18px;
  margin-left: 10px;
}

.sczt-left,
.sczt-echarts,
.cyfb-con,
.cyjj-con {
  width: 49%;
  height: 100%;
}

#cyfb {
  width: 100%;
  height: 500px;
}

#cyl-echarts,
#zdqy-echarts {
  width: 100%;
  height: 430px;
}

#cyjj-chart {
  width: 620px;
  height: 520px;
}

/* 表格 */
.table {
  width: 960px;
  height: 500px;
  /* padding: 10px; */
  /* box-sizing: border-box; */
}

.table1 {
  width: 980px;
  height: 740px;
}

.table1 .th {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  line-height: 60px;
  color: #dcefff;
  margin-left: 0;
  text-align: center;
}

.table1 .th_td {
  letter-spacing: 0px;
  text-align: center;
}

.table1 .tbody {
  width: 100%;
  height: calc(100% - 110px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table1 .tbody:hover {
  overflow-y: auto;
}

.table1 .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table1 .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table1 .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 78px;
  line-height: 78px;
  font-size: 28px;
  color: #dcefff;
  /* cursor: pointer; */
  box-sizing: border-box;
  background-color: #0f2b4d;
  /* border-bottom: 1px solid #1d3c5f; */
  margin-bottom: 0;
}

.table1 .tr_td {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  letter-spacing: 0px;
  text-align: center;
  box-sizing: border-box;
}

.table1 .tr_td > img {
  position: relative;
  top: 25px;
}

.table1 .tr:nth-child(2n) {
  background: #19345833;
  /* background: rgba(10, 39, 76); */
}

.table1 .tr:nth-child(2n + 1) {
  /* background: rgba(15, 49, 95); */
  background: #19345899;
}

.table1 .tr:hover {
  background-color: #3e74aa70;
}

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.title-item {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
}

.title-item > li {
  margin-right: 50px;
}

.title-item > li:last-child {
  margin-right: 0;
}

.title-item-active {
  color: #fff;
}

.qhzc-bottom {
  width: 100%;
  height: 100px !important;
  font-size: 30px;
  position: absolute;
  bottom: 10px;
}

.hqzc-top {
  width: 100%;
  height: 590px;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
}

.zcdf {
  width: 100%;
  font-size: 30px;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.zcdfValue {
  font-size: 40px;
  color: #ff941d;
}

.data-page {
  width: 960px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 新的分页样式 */
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background: #5f7b96;
  color: #fff;
  font-size: 34px;
  font-weight: normal;
  height: 60px;
  line-height: 58px;
  box-sizing: border-box;
}

.el-pager li.active + li {
  border-left: 1px solid transparent !important;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background: #0166a6;
  border-radius: 3px 3px 3px 3px;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
}

.el-pager li {
  background: #5f7b96;
  padding: 0 10px;
  border: 1px solid transparent;
  box-sizing: border-box;
}

.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 30px;
}

.el-pagination .btn-next,
.el-pagination .btn-prev {
  width: 60px;
}

/* 分页- */
.el-pagination__total {
  color: #fff;
  font-size: 32px !important;
  margin: 0;
  padding-right: 20px;
}

/* 分页-多少页 */
.el-pagination__sizes .el-input .el-input__inner {
  font-size: 32px;
  background-color: transparent;
  border-color: #6f788a;
  height: 60px;
  line-height: 60px;
  color: #fff;
}

.el-pagination .el-select .el-input .el-input__inner {
  padding-right: 51px;
}

.el-select .el-input .el-select__caret {
  font-size: 40px;
  color: #6f788a;
  transform: rotate(0);
  padding-top: 10px;
}

.el-select .el-input .el-select__caret.is-reverse {
  transform: rotate(180deg);
  margin-top: -10px;
}

.el-pagination .el-select .el-input {
  width: 250px;
  margin: 0;
}

.el-pagination button,
.el-pagination span:not([class*='suffix']) {
  height: 60px;
  line-height: 60px;
}

.el-pagination .el-input__suffix {
  right: 15px;
}

.el-select-dropdown__item {
  font-size: 32px;
  background-color: transparent;
  color: #fff;
  border-color: #6f788a;
  margin-bottom: 10px;
}

.el-select-dropdown {
  background-color: #041330;
  border-color: #e4e7ed4f;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: transparent;
}

.el-popper[x-placement^='bottom'] .popper__arrow {
  display: none;
}

.el-pagination button,
.el-pagination span:not([class*='suffix']) {
  font-size: 32px;
  color: #fff;
}

.el-pagination__editor.el-input .el-input__inner {
  height: 60px;
  font-size: 32px;
  background-color: transparent;
  color: #fff;
  border-color: #6f788a;
}

.el-pagination__editor.el-input {
  width: 100px;
  height: 60px;
  margin: 0 10px;
}

[v-cloak] {
  display: none;
}
/* 标题 */
.first-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/first-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}
.first-title > i {
  position: absolute;
  width: 1062px;
  height: 90px;
  display: inline-block;
  background: url('@/pages/qyzf/img/line.png');
  background-size: 100%;
  animation-name: animateLine;
  animation-duration: 10s;
  animation-iteration-count: infinite;
  /* animation-direction:alternate; */
  /* animation-delay */
}
@keyframes animateLine {
  0% {
    left: -20%;
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 70%;
    opacity: 0.2;
  }
}
.second-title {
  width: 100%;
  height: 90px;
  font-size: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dcefff;
  background-image: url('@/pages/qyzf/img/second-title.png');
  background-size: 100% 100%;
  /* background: linear-gradient( 268deg, rgba(0,121,227,0) 0%, rgba(28,148,249,0.5882) 51%, rgba(8,17,26,0) 100%); */
  border-radius: 0px 0px 0px 0px;
  margin-bottom: 20px;
  position: relative;
}
/* 鼠标禁用事件 */
.mouse-no {
  pointer-events: none;
}
.mouse-pointer {
  cursor: pointer;
}
.mouse-not {
  /* cursor: not-allowed; */
  cursor: default;
}
/* 效果 */
.red-color {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.yel-color {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

.blue-color {
  background-image: linear-gradient(#caffff 10%, #ffffff 60%, #00c0ff 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.orange-color {
  background-image: linear-gradient(#ffe2cd 10%, #ffffff 60%, #fd852e 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.green-color {
  background-image: linear-gradient(#f0ffd7 10%, #ffffff 60%, #a9db52 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
/* 表格 */
.table {
  width: 100%;
}

.table-th {
  width: 100%;
  height: 60px;
  background-color: #00396f;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.th {
  flex: 0.3;
  font-size: 32px;
  /* text-align: center; */
  color: #77b3f1;
  margin-left: 10px;
}

.table-tr {
  width: 100%;
  height: 288px;
  overflow-y: auto;
}

.table-tr::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.table-tr::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.tr {
  width: 100%;
  padding: 10px 0;
  background-color: #0f2b4d;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.td {
  flex: 0.3;
  font-size: 32px;
  color: #fff;
  margin-left: 10px;
  /* text-align: center; */
}
.td > span:last-child {
  margin-left: 10px;
}

/* 下拉框 */

.select {
  display: inline-block;
  width: 300px;
  height: 55px;
  text-align: right;
  position: absolute;
  right: 120px;
  top: 0;
  z-index: 100;
}

.flow-icon {
  width: 25px;
  position: absolute;
  top: 20px;
  right: 14px;
}

.flow-icon > img {
  width: 25px;
}

.ul > div {
  width: 100%;
  height: 60px;
  line-height: 60px;
}

.ul {
  width: 100%;
  height: 60px;
  text-align: center;
  font-size: 32px;
  color: #fefefe;
  background-color: #132c4e;
  border: 1px solid #359cf8;
  border-radius: 40px;
  margin-top: 25px;
}
.ul > ul {
  overflow-y: auto;
  display: none;
}
.ul ul > li {
  width: 100%;
  height: 62px;
  line-height: 62px;
  background-color: #132c4ec2;
  padding-right: 20px;
  box-sizing: border-box;
}

.ul ul > li:hover {
  background-color: #359cf8;
}

.ul-active {
  display: block !important;
}

.ul > ul > li:first-of-type {
  border-radius: 40px 40px 0 0;
}

.ul ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.ul ul::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}
.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 5px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 5px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.4);
}
</style>