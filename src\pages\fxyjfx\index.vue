<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 11:38:22
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-11 15:25:44
-->
<template>
  <div class="qyzf-container">
    <div class="inneLeft">
      <fxyjfxLeft class="animate__animated animate__fadeInLeft" />
    </div>
    <div class="midBottom animate__animated animate__fadeIn">
      <fxyjfxCenter />
    </div>
    <div class="innerRight">
      <fxyjfxRight class="animate__animated animate__fadeInLeft" />
    </div>
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import fxyjfxLeft from '@/pages/fxyjfx/components/fxyjfxLeft'
import fxyjfxCenter from '@/pages/fxyjfx/components/fxyjfxCenter'
import fxyjfxRight from '@/pages/fxyjfx/components/fxyjfxRight'
export default {
  name: 'index',
  data() {
    return {
      visible: false,
      qyInfoVisible: false,
      qylx: '',
      allMessage: [],
    }
  },
  components: {
    wrapbox,
    fxyjfxLeft,
    fxyjfxCenter,
    fxyjfxRight,
  },
  computed: {},
  mounted() {},
  methods: {
  },
  watch: {},
}
</script>

<style scoped lang="less">
.qyzf-container {
  position: relative;
  width: 100%;
  height: 100%;
  // z-index: 2;
  // background: url('~@/assets/application/dtBg.png') no-repeat center center;
  // background-size: 100% 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .midBottom {
    position: absolute;
    left: 2135px;
    top: 229px;
    z-index: 2;
  }
  /deep/ .customClass {
    background-color: rgb(77, 91, 175);
  }
}
</style>
