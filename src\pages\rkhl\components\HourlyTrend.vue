<template>
  <div class="chart-item">
    <SubTitle title="24小时实时人口变化趋势" />
    <div class="chart-container">
      <div class="chart-controls">
        <OptionsSwitcher v-model="selectedOption" :options="options" />
      </div>
      <div id="hourlyTrend" class="chart"></div>
    </div>
  </div>
</template>

<script>
import OptionsSwitcher from '@/components/OptionsSwitcher.vue'
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'HourlyTrend',
  components: {
    SubTitle,
    OptionsSwitcher,
  },
  data() {
    return {
      selectedOption: '全部',
      options: [
        { value: '全部', label: '全部' },
        { value: '男性', label: '男性' },
        { value: '女性', label: '女性' },
      ],
    }
  },
  mounted() {
    this.initHourlyTrend()
  },
  methods: {
    // 24小时趋势折线图
    initHourlyTrend() {
      const chart = this.$echarts.init(document.getElementById('hourlyTrend'))
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 14 },
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '20%',
          bottom: '20%',
        },
        xAxis: {
          type: 'category',
          data: ['06:18', '09:18', '12:18', '15:18', '18:18', '21:18', '24:18'],
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            type: 'line',
            data: [400, 300, 200, 100, 300, 400, 350],
            smooth: true,
            lineStyle: { color: '#00c0ff', width: 2 },
            itemStyle: { color: '#00c0ff' },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(0, 192, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 192, 255, 0)' },
              ]),
            },
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 10;

      .control-btn {
        width: 120px;
        height: 40px;
        font-size: 24px;
        font-weight: 400;
        color: #bfbcbc;
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          font-weight: 700;
          background: rgba(0, 74, 166, 0.8);
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
